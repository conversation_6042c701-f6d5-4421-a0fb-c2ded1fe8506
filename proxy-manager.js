const proxifly = require('proxifly');
const axios = require('axios');
const fs = require('fs');
const FreeProxyScraper = require('./freeproxy-scraper');

class ProxyManager {
    constructor() {
        this.proxies = [];
        this.workingProxies = [];
        this.lastUpdate = 0;
        this.updateInterval = 5 * 60 * 1000; // 5 Minuten
        this.proxiflyClient = new proxifly({
            // Kein API-Key erforderlich für grundlegende Nutzung
        });

        // Backup der alten Proxy-Konfiguration
        this.backupOldProxyConfig();

        // Initialer Proxy-Load
        this.updateProxies();

        // Automatische Aktualisierung alle 5 Minuten
        setInterval(() => {
            this.updateProxies();
        }, this.updateInterval);
    }

    backupOldProxyConfig() {
        try {
            // Erstelle proxy_archiv Verzeichnis falls es nicht existiert
            if (!fs.existsSync('./proxy_archiv')) {
                fs.mkdirSync('./proxy_archiv', { recursive: true });
            }

            if (fs.existsSync('./proxys.txt')) {
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                fs.copyFileSync('./proxys.txt', `./proxy_archiv/proxys_backup_${timestamp}.txt`);
                console.log('✓ Alte Proxy-Konfiguration gesichert');
            }
            if (fs.existsSync('./proxy_auth.txt')) {
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                fs.copyFileSync('./proxy_auth.txt', `./proxy_archiv/proxy_auth_backup_${timestamp}.txt`);
                console.log('✓ Alte Proxy-Authentifizierung gesichert');
            }
        } catch (error) {
            console.log('Warnung: Konnte alte Proxy-Konfiguration nicht sichern:', error.message);
        }
    }

    async fetchProxiesFromCountry(country, protocol = 'http') {
        try {
            const options = {
                protocol: protocol,
                country: country,
                anonymity: ['anonymous', 'elite'], // Nur anonyme Proxys
                https: true,
                speed: 10000, // Max 10 Sekunden Antwortzeit
                format: 'json',
                quantity: 1
            };

            const proxy = await this.proxiflyClient.getProxy(options);
            return proxy;
        } catch (error) {
            console.log(`Fehler beim Abrufen von ${country} Proxys:`, error.message);
            return null;
        }
    }

    async fetchProxiesFromDirectURL(country) {
        try {
            const url = `https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/countries/${country}/data.json`;
            const response = await axios.get(url, { timeout: 10000 });

            if (response.data && Array.isArray(response.data)) {
                // Filtere nur HTTP/HTTPS Proxys mit guter Anonymität
                return response.data.filter(proxy =>
                    (proxy.protocol === 'http' || proxy.protocol === 'https') &&
                    (proxy.anonymity === 'anonymous' || proxy.anonymity === 'elite') &&
                    proxy.speed < 10000
                );
            }
            return [];
        } catch (error) {
            console.log(`Fehler beim direkten Abrufen von ${country} Proxys:`, error.message);
            return [];
        }
    }

    async testProxy(proxy) {
        try {
            // Verwende einfachere Test-URLs die weniger blockiert werden
            const testUrls = [
                'http://icanhazip.com',
                'http://ipinfo.io/ip',
                'http://httpbin.org/ip',
                'http://checkip.amazonaws.com'
            ];

            for (const testUrl of testUrls) {
                try {
                    const response = await axios.get(testUrl, {
                        proxy: {
                            host: proxy.ip,
                            port: proxy.port,
                            protocol: 'http'
                        },
                        timeout: 3000, // Kürzerer Timeout
                        headers: {
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                        }
                    });

                    if (response.status === 200) {
                        console.log(`✓ Proxy ${proxy.ip}:${proxy.port} funktioniert`);
                        return true;
                    }
                } catch (testError) {
                    // Versuche nächste URL
                    continue;
                }
            }

            return false;
        } catch (error) {
            // Stille Fehler für bessere Performance
            return false;
        }
    }

    async updateProxies() {
        console.log('\n🔄 Aktualisiere Proxy-Liste...');
        const countries = ['DE', 'AT', 'CH']; // Deutschland, Österreich, Schweiz
        const newProxies = [];

        for (const country of countries) {
            console.log(`📡 Lade Proxys aus ${country}...`);

            // Verwende direkten Download als Hauptmethode
            try {
                const directProxies = await this.fetchProxiesFromDirectURL(country);
                if (directProxies.length > 0) {
                    // Nimm die ersten 10 besten Proxys
                    newProxies.push(...directProxies.slice(0, 10));
                    console.log(`✓ ${directProxies.length} Proxys aus ${country} gefunden`);
                }
            } catch (error) {
                console.log(`⚠️ Direkter Download für ${country} fehlgeschlagen: ${error.message}`);
            }

            // Kurze Pause zwischen Ländern
            await this.sleep(1000);
        }

        // Fallback 1: Versuche FreeProxy.World Scraping
        if (newProxies.length === 0) {
            console.log('🔄 Versuche FreeProxy.World Scraping...');
            try {
                const scrapedProxies = await this.scrapeFreeProxyWorld();
                if (scrapedProxies.length > 0) {
                    newProxies.push(...scrapedProxies);
                    console.log(`✓ ${scrapedProxies.length} Proxys von FreeProxy.World gescraped`);
                }
            } catch (error) {
                console.log(`⚠️ FreeProxy.World Scraping fehlgeschlagen: ${error.message}`);
            }
        }

        // Fallback 2: Lade alle HTTP Proxys von anderen Quellen
        if (newProxies.length === 0) {
            console.log('🔄 Lade allgemeine HTTP Proxys als Fallback...');

            // Versuche verschiedene Proxy-Quellen
            const proxySources = [
                'https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/protocols/http/data.json',
                'https://raw.githubusercontent.com/clarketm/proxy-list/master/proxy-list-raw.txt',
                'https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/http.txt',
                'https://raw.githubusercontent.com/roosterkid/openproxylist/main/HTTPS_RAW.txt',
                'https://raw.githubusercontent.com/monosans/proxy-list/main/proxies/http.txt'
            ];

            for (const source of proxySources) {
                try {
                    if (source.endsWith('.json')) {
                        const response = await axios.get(source, { timeout: 10000 });
                        if (response.data && Array.isArray(response.data)) {
                            const filteredProxies = response.data.filter(proxy =>
                                (proxy.anonymity === 'anonymous' || proxy.anonymity === 'elite') &&
                                proxy.speed < 5000
                            ).slice(0, 15);
                            newProxies.push(...filteredProxies);
                            console.log(`✓ ${filteredProxies.length} Proxys von JSON-Quelle geladen`);
                        }
                    } else {
                        // Text-basierte Proxy-Liste
                        const response = await axios.get(source, { timeout: 10000 });
                        if (response.data) {
                            const proxyLines = response.data.split('\n').filter(line =>
                                line.trim() && line.includes(':') && !line.startsWith('#')
                            ).slice(0, 10);

                            for (const line of proxyLines) {
                                const [ip, port] = line.trim().split(':');
                                if (ip && port && !isNaN(port)) {
                                    newProxies.push({
                                        ip: ip,
                                        port: parseInt(port),
                                        protocol: 'http',
                                        anonymity: 'unknown',
                                        speed: 5000
                                    });
                                }
                            }
                            console.log(`✓ ${proxyLines.length} Proxys von Text-Quelle geladen`);
                        }
                    }

                    if (newProxies.length > 0) break; // Stoppe wenn Proxys gefunden

                } catch (error) {
                    console.log(`⚠️ Quelle ${source} fehlgeschlagen: ${error.message}`);
                }

                await this.sleep(1000);
            }
        }

        console.log(`📊 ${newProxies.length} Proxys gefunden, teste Funktionalität...`);

        // Teste mehr Proxys um bessere Chancen zu haben
        const proxiesToTest = newProxies.slice(0, 30); // Teste bis zu 30 Proxys
        const workingProxies = [];

        console.log('🔍 Teste Proxys parallel für bessere Performance...');

        // Teste Proxys in kleineren Batches parallel
        const batchSize = 5;
        for (let i = 0; i < proxiesToTest.length; i += batchSize) {
            const batch = proxiesToTest.slice(i, i + batchSize);

            // Teste Batch parallel
            const batchResults = await Promise.allSettled(
                batch.map(proxy => this.testProxy(proxy))
            );

            // Sammle funktionierende Proxys
            for (let j = 0; j < batch.length; j++) {
                if (batchResults[j].status === 'fulfilled' && batchResults[j].value === true) {
                    workingProxies.push(batch[j]);
                }
            }

            // Stoppe wenn wir genügend funktionierende Proxys haben
            if (workingProxies.length >= 10) {
                console.log('✓ Genügend funktionierende Proxys gefunden, stoppe Tests');
                break;
            }

            // Kurze Pause zwischen Batches
            await this.sleep(1000);
        }

        this.workingProxies = workingProxies;
        this.lastUpdate = Date.now();

        console.log(`✅ ${workingProxies.length} funktionierende Proxys gefunden`);

        // Speichere funktionierende Proxys in Datei
        this.saveProxiesToFile();

        return workingProxies;
    }

    saveProxiesToFile() {
        try {
            const proxyStrings = this.workingProxies.map(proxy => `${proxy.ip}:${proxy.port}`);
            fs.writeFileSync('./proxys.txt', proxyStrings.join('\n'));

            // Erstelle auch eine detaillierte JSON-Datei
            fs.writeFileSync('./proxys_detailed.json', JSON.stringify(this.workingProxies, null, 2));

            console.log(`💾 ${proxyStrings.length} Proxys in proxys.txt gespeichert`);
        } catch (error) {
            console.log('Fehler beim Speichern der Proxys:', error.message);
        }
    }

    getRandomProxy() {
        if (this.workingProxies.length === 0) {
            console.log('⚠️ Keine funktionierenden Proxys verfügbar!');
            // Fallback: Verwende einen Standard-Proxy oder null für direkten Zugang
            return null;
        }

        const randomIndex = Math.floor(Math.random() * this.workingProxies.length);
        const proxy = this.workingProxies[randomIndex];
        return `${proxy.ip}:${proxy.port}`;
    }

    getProxyList() {
        return this.workingProxies.map(proxy => `${proxy.ip}:${proxy.port}`);
    }

    getProxyCount() {
        return this.workingProxies.length;
    }

    getLastUpdateTime() {
        return new Date(this.lastUpdate).toLocaleString('de-DE');
    }

    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Neue Methode: FreeProxy.World Scraping
    async scrapeFreeProxyWorld() {
        try {
            console.log('🕷️ Starte FreeProxy.World Scraping...');
            const scrapedProxies = await FreeProxyScraper.scrapeProxies();

            // Filtere und formatiere die Proxys
            const formattedProxies = scrapedProxies.filter(proxy =>
                proxy.ip && proxy.port && proxy.speed < 3000
            ).map(proxy => ({
                ip: proxy.ip,
                port: proxy.port,
                protocol: proxy.protocol || 'http',
                anonymity: proxy.anonymity || 'anonymous',
                speed: proxy.speed || 5000,
                geolocation: proxy.geolocation || { country: 'DE' }
            }));

            console.log(`✓ ${formattedProxies.length} Proxys von FreeProxy.World formatiert`);
            return formattedProxies;

        } catch (error) {
            console.log(`⚠️ FreeProxy.World Scraping Fehler: ${error.message}`);
            return [];
        }
    }

    // Manuelle Proxy-Aktualisierung
    async forceUpdate() {
        console.log('🔄 Erzwinge Proxy-Aktualisierung...');
        return await this.updateProxies();
    }

    // Entferne einen defekten Proxy aus der Liste
    removeProxy(proxyString) {
        const [ip, port] = proxyString.split(':');
        this.workingProxies = this.workingProxies.filter(proxy =>
            !(proxy.ip === ip && proxy.port.toString() === port)
        );
        this.saveProxiesToFile();
        console.log(`🗑️ Proxy ${proxyString} aus der Liste entfernt`);
    }

    // Statistiken anzeigen
    getStats() {
        return {
            totalProxies: this.workingProxies.length,
            lastUpdate: this.getLastUpdateTime(),
            countries: [...new Set(this.workingProxies.map(p => p.geolocation?.country))],
            protocols: [...new Set(this.workingProxies.map(p => p.protocol))]
        };
    }
}

module.exports = ProxyManager;
