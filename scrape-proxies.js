#!/usr/bin/env node

const ProxyManager = require('./proxy-manager');
const readlineSync = require('readline-sync');

class ProxyScraper {
    constructor() {
        this.proxyManager = new ProxyManager(false); // Kein automatisches Laden
    }

    async showMenu() {
        console.log('\n🕷️ EventimBot Proxy-Scraper');
        console.log('============================');
        console.log('1. Proxys von allen Quellen scrapen');
        console.log('2. Nur FreeProxy.World scrapen');
        console.log('3. Nur Proxifly API verwenden');
        console.log('4. Aktuelle Proxy-Liste anzeigen');
        console.log('5. Proxy-Test durchführen');
        console.log('0. Beenden');
        console.log('============================');

        const choice = readlineSync.question('Wählen Sie eine Option: ');
        await this.handleChoice(choice);
    }

    async handleChoice(choice) {
        switch (choice) {
            case '1':
                await this.scrapeAllSources();
                break;
            case '2':
                await this.scrapeFreeProxyWorld();
                break;
            case '3':
                await this.scrapeProxifly();
                break;
            case '4':
                this.showProxyList();
                break;
            case '5':
                await this.testProxies();
                break;
            case '0':
                console.log('👋 Auf Wiedersehen!');
                process.exit(0);
                break;
            default:
                console.log('❌ Ungültige Auswahl');
        }

        // Zurück zum Menü
        await this.sleep(2000);
        await this.showMenu();
    }

    async scrapeAllSources() {
        console.log('\n🔄 Starte Proxy-Scraping von allen Quellen...');
        await this.proxyManager.updateProxies();
        console.log('✅ Scraping abgeschlossen');
        this.showStats();
    }

    async scrapeFreeProxyWorld() {
        console.log('\n🕷️ Starte FreeProxy.World Scraping...');
        const proxies = await this.proxyManager.scrapeFreeProxyWorld();
        
        if (proxies.length > 0) {
            console.log(`📊 ${proxies.length} Proxys gefunden, teste Funktionalität...`);
            const workingProxies = await this.proxyManager.testProxies(proxies);
            
            this.proxyManager.workingProxies = workingProxies;
            this.proxyManager.saveProxiesToFile();
            
            console.log(`✅ ${workingProxies.length} funktionierende Proxys gefunden und gespeichert`);
        } else {
            console.log('❌ Keine Proxys gefunden');
        }
    }

    async scrapeProxifly() {
        console.log('\n📡 Starte Proxifly API Scraping...');
        const countries = ['DE', 'AT', 'CH'];
        const allProxies = [];

        for (const country of countries) {
            console.log(`📡 Lade Proxys aus ${country}...`);
            const proxies = await this.proxyManager.fetchProxiesFromDirectURL(country);
            allProxies.push(...proxies);
        }

        if (allProxies.length > 0) {
            console.log(`📊 ${allProxies.length} Proxys gefunden, teste Funktionalität...`);
            const workingProxies = await this.proxyManager.testProxies(allProxies);
            
            this.proxyManager.workingProxies = workingProxies;
            this.proxyManager.saveProxiesToFile();
            
            console.log(`✅ ${workingProxies.length} funktionierende Proxys gefunden und gespeichert`);
        } else {
            console.log('❌ Keine Proxys gefunden');
        }
    }

    showProxyList() {
        const proxies = this.proxyManager.getProxyList();
        console.log('\n📋 Aktuelle Proxy-Liste:');
        if (proxies.length === 0) {
            console.log('❌ Keine Proxys verfügbar');
        } else {
            proxies.forEach((proxy, index) => {
                console.log(`${index + 1}. ${proxy}`);
            });
        }
    }

    async testProxies() {
        console.log('\n🧪 Teste alle verfügbaren Proxys...');
        if (this.proxyManager.workingProxies.length === 0) {
            console.log('❌ Keine Proxys zum Testen verfügbar');
            return;
        }

        const workingProxies = await this.proxyManager.testProxies(this.proxyManager.workingProxies);
        this.proxyManager.workingProxies = workingProxies;
        this.proxyManager.saveProxiesToFile();
        
        console.log(`✅ ${workingProxies.length} funktionierende Proxys gefunden`);
    }

    showStats() {
        const stats = this.proxyManager.getStats();
        console.log('\n📊 Proxy-Statistiken:');
        console.log(`Anzahl funktionierender Proxys: ${stats.totalProxies}`);
        console.log(`Verfügbare Länder: ${stats.countries.join(', ')}`);
        console.log(`Protokolle: ${stats.protocols.join(', ')}`);
    }

    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Starte das Tool
async function main() {
    console.log('🚀 EventimBot Proxy-Scraper gestartet');
    console.log('=====================================');
    console.log('Dieses Tool scrapt Proxys und speichert sie in proxys.txt');
    console.log('Der Bot kann dann diese Proxys verwenden.\n');

    const scraper = new ProxyScraper();
    await scraper.showMenu();
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = ProxyScraper;
